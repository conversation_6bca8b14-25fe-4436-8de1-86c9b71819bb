import React from 'react';
import { JsonView, defaultStyles } from 'react-json-view-lite';
import 'react-json-view-lite/dist/index.css';

// Example component showing react-json-view-lite usage
export function JsonViewExample() {
  const sampleData = {
    name: "<PERSON>",
    age: 30,
    isActive: true,
    address: {
      street: "123 Main St",
      city: "New York",
      zipCode: "10001"
    },
    hobbies: ["reading", "coding", "hiking"]
  };

  // Custom styling example
  const customStyles = {
    container: 'border border-gray-200 rounded p-4',
    value: 'text-blue-600',
    string: 'text-green-500',
    number: 'text-red-500',
    boolean: 'text-purple-500',
    null: 'text-gray-500',
  };

  return (
    <div>
      <h3>Default Styling:</h3>
      <JsonView data={sampleData} style={defaultStyles} />

      <h3>Custom Styling:</h3>
      <JsonView data={sampleData} style={customStyles} />
    </div>
  );
}