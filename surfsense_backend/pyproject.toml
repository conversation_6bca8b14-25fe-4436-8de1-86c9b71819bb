[project]
name = "surf-new-backend"
version = "0.0.7"
description = "SurfSense Backend"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alembic>=1.13.0",
    "asyncpg>=0.30.0",
    "chonkie[all]>=1.0.6",
    "discord-py>=2.5.2",
    "fastapi>=0.115.8",
    "fastapi-users[oauth,sqlalchemy]>=14.0.1",
    "firecrawl-py>=1.12.0",
    "github3.py==4.0.1",
    "langchain-community>=0.3.17",
    "langchain-unstructured>=0.1.6",
    "langgraph>=0.3.29",
    "linkup-sdk>=0.2.4",
    "litellm>=1.61.4",
    "llama-cloud-services>=0.6.25",
    "markdownify>=0.14.1",
    "notion-client>=2.3.0",
    "pgvector>=0.3.6",
    "playwright>=1.50.0",
    "python-ffmpeg>=2.0.12",
    "rerankers[flashrank]>=0.7.1",
    "sentence-transformers>=3.4.1",
    "slack-sdk>=3.34.0",
    "static-ffmpeg>=2.13",
    "tavily-python>=0.3.2",
    "unstructured-client>=0.30.0",
    "unstructured[all-docs]>=0.16.25",
    "uvicorn[standard]>=0.34.0",
    "validators>=0.34.0",
    "youtube-transcript-api>=1.0.3",
]
